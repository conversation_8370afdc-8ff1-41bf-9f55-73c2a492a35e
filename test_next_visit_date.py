#!/usr/bin/env python3
"""
Test script to verify next visit date functionality
"""

import pandas as pd
import os
import traceback
from datetime import datetime, timedelta

# Test imports
try:
    from next_order.data_handling.extract_data import extract_dataset
    from next_order.data_handling.clean_data import clean_dataset
    from next_order.prediction_and_output.prediction import prediction
    print("✅ All modules imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    traceback.print_exc()
    exit(1)

def test_next_visit_date():
    """Test the next visit date functionality"""
    print("\n🧪 Testing Next Visit Date Functionality")
    print("=" * 50)
    
    # Test parameters
    visit_freq = 7
    csv_data = "E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/ranith_data_logs/ranith.csv"
    
    # Check if file exists
    if not os.path.exists(csv_data):
        print(f"❌ CSV file not found: {csv_data}")
        return False
    
    print(f"✅ CSV file found: {csv_data}")
    
    try:
        # Load data
        print("\n📊 Loading data...")
        full_data = pd.read_csv(csv_data)
        print(f"✅ Data loaded successfully. Shape: {full_data.shape}")
        print(f"✅ Columns: {list(full_data.columns)}")
        
        # Get first outlet for testing
        outlet_ids = list(full_data['customercode'].unique())
        test_outlet_id = outlet_ids[0]
        print(f"✅ Testing with outlet ID: {test_outlet_id}")
        
        # Get outlet data
        outlet_data = full_data[full_data['customercode'] == test_outlet_id]
        print(f"✅ Outlet data shape: {outlet_data.shape}")
        
        # Extract dataset
        print("\n🔍 Extracting dataset...")
        extracted_dataset = extract_dataset(outlet_data, test_outlet_id)
        print(f"✅ Extracted dataset shape: {extracted_dataset.shape}")
        print(f"✅ Extracted dataset columns: {list(extracted_dataset.columns)}")
        
        # Clean dataset
        print("\n🧹 Cleaning dataset...")
        cleaned_data, ProductIDs, next_visit_date, outlier_product_ids = clean_dataset(extracted_dataset, visit_freq)
        print(f"✅ Cleaned data shape: {cleaned_data.shape}")
        print(f"✅ Number of ProductIDs: {len(ProductIDs)}")
        print(f"✅ Next visit date: {next_visit_date}")
        print(f"✅ Next visit date type: {type(next_visit_date)}")
        print(f"✅ Outlier product IDs: {len(outlier_product_ids)}")
        
        # Make predictions
        print("\n🔮 Making predictions...")
        predicted_df = prediction(ProductIDs, cleaned_data, next_visit_date, visit_freq)
        print(f"✅ Predicted DataFrame shape: {predicted_df.shape}")
        
        if not predicted_df.empty:
            print(f"✅ Predicted DataFrame columns: {list(predicted_df.columns)}")
            print("\n📋 Sample predictions:")
            print(predicted_df.head())
            
            # Test next visit date column
            if 'Next_Visit_Date' in predicted_df.columns:
                print(f"✅ 'Next_Visit_Date' column found")
                sample_date = predicted_df['Next_Visit_Date'].iloc[0]
                print(f"✅ Sample next visit date: {sample_date}")
                print(f"✅ Sample next visit date type: {type(sample_date)}")
                
                # Test date conversion
                if pd.notnull(sample_date):
                    if isinstance(sample_date, pd.Timestamp):
                        date_str = sample_date.strftime('%Y-%m-%d %H:%M:%S')
                        print(f"✅ Converted to MySQL format: {date_str}")
                    else:
                        print(f"⚠️  Date is not a Timestamp: {type(sample_date)}")
                else:
                    print("⚠️  Sample date is null")
            else:
                print("❌ 'Next_Visit_Date' column not found")
                print(f"Available columns: {list(predicted_df.columns)}")
        else:
            print("⚠️  Predicted DataFrame is empty")
        
        print("\n✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Next Visit Date Test")
    success = test_next_visit_date()
    if success:
        print("\n🎉 All tests passed!")
    else:
        print("\n💥 Tests failed!")
